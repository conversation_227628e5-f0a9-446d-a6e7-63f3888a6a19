
import { BatterySettingsUI } from '../settings/BatterySettingsUI';

// 初始化設定頁面功能
export function initSettings() {
  // 主題切換功能
  const themeToggle = document.getElementById('theme-toggle');
  const themeIcon = document.getElementById('theme-icon');
  const themeText = document.getElementById('theme-text');
  const body = document.body;
  
  // 檢查必要元素是否存在
  if (!themeToggle || !themeIcon || !themeText) {
      console.log('Theme toggle elements not found');
      return;
  }
  
  // 檢查本地存儲中的主題設置
  const currentTheme = localStorage.getItem('theme') || 'dark';
  //console.log("Current theme from localStorage:", currentTheme); // 調試日誌
  setTheme(currentTheme);
  
  // 主題切換按鈕點擊事件
  themeToggle.addEventListener('click', () => {
    //console.log("Theme toggle clicked"); // 調試日誌
    const newTheme = body.classList.contains('light-mode') ? 'dark' : 'light';
    //console.log("Switching to theme:", newTheme); // 調試日誌
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    
    // 觸發全局主題變更事件，讓其他頁面也能響應
    const themeChangeEvent = new CustomEvent('themeChange', { detail: { theme: newTheme } });
    document.dispatchEvent(themeChangeEvent);
  });

  // 設置主題函數
  function setTheme(theme : string) {
      // 檢查必要元素是否存在
      if (!themeToggle || !themeIcon || !themeText) {
          console.error('Theme toggle elements not found');
          return;
      }
      if (theme === 'light') {
          body.classList.add('light-mode');
          themeToggle.classList.remove('bg-accent-yellow');
          themeToggle.classList.add('bg-gray-700');
          themeIcon.innerHTML = `
          <path fill-rule="evenodd" d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" style="fill:none;stroke:white;stroke-width:1" clip-rule="evenodd"></path>
          `;
          themeText.textContent = 'light-mode';
      } else {
          body.classList.remove('light-mode');
          themeToggle.classList.add('bg-accent-yellow');
          themeToggle.classList.remove('bg-gray-700');
          themeIcon.innerHTML = `
          <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" style="fill:none;stroke:yellow;stroke-width:1" clip-rule="evenodd"></path>
          `;
          themeText.textContent = 'dark-mode';
      }
      
      // 應用主題相關的 CSS 變數
      // if (theme === 'light') {
      //     document.documentElement.style.setProperty('--bg-color', '#f8f9fa');
      //     document.documentElement.style.setProperty('--text-color', '#333333');
      // } else {
      //   // 切換暗黑模式
      //     document.documentElement.style.setProperty('--bg-color', '#1E2126');
      //     document.documentElement.style.setProperty('--text-color', '#f6f6f6');
      // }
  }
  
  // 進階設定按鈕功能
  const advancedSettingsButton = document.getElementById('advanced-settings-button');
  if (advancedSettingsButton) {
    advancedSettingsButton.addEventListener('click', () => {
      showAdvancedSettingsModal();
    });
  }

  // 幫助按鈕功能
  const helpButton = document.getElementById('help-button');
  // 檢查必要元素是否存在
  if (!helpButton) {
      console.error('help button not found');
      return;
  }
  helpButton.addEventListener('click', () => {
    showHelpModal();
  });
  
  // 顯示幫助模態框
  function showHelpModal() {
    // 檢查當前主題
    const isLightMode = document.body.classList.contains('light-mode');
    
    // 創建模態框元素
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="fixed inset-0 bg-black opacity-50"></div>
      <div class="${isLightMode ? 'bg-white' : 'bg-darker-gray'} rounded-lg p-6 max-w-lg w-full mx-4 z-10 shadow-lg">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold ${isLightMode ? 'text-gray-800' : 'text-white'}">使用指南</h3>
          <button id="close-modal" class="${isLightMode ? 'text-gray-600 hover:text-gray-800' : 'text-gray-400 hover:text-white'}">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
        <div class="${isLightMode ? 'text-gray-700' : 'text-gray-300'}">
          <h4 class="font-semibold mb-2">基本導航</h4>
          <p class="mb-3">使用左側側邊欄導航到不同的頁面。每個頁面提供不同的功能和數據視圖。</p>
          
          <h4 class="font-semibold mb-2">主題切換</h4>
          <p class="mb-3">您可以在系統設定中切換亮色和暗色模式，以適應不同的使用環境。</p>
          
          <h4 class="font-semibold mb-2">數據管理</h4>
          <p class="mb-3">在各個頁面中，您可以查看、添加、編輯和刪除相關數據。表格視圖提供了數據的概覽。</p>
          
          <h4 class="font-semibold mb-2">快捷操作</h4>
          <p>使用頁面頂部的操作按鈕進行快速操作，如添加新記錄或刷新數據。</p>
        </div>
        <div class="mt-6 text-center">
          <button id="confirm-modal" class="px-4 py-2 ${isLightMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-yellow-400 bg-accent-yellow hover:bg-yellow-600'} ${isLightMode ? 'text-white' : 'text-black'} rounded font-medium transition-colors">了解</button>
        </div>
      </div>
    `;
    
    // 添加到文檔
    document.body.appendChild(modal);
    
    // 獲取按鈕元素並進行空值檢查
    const closeModalButton = document.getElementById('close-modal');
    const confirmModalButton = document.getElementById('confirm-modal');
    
    if (!closeModalButton || !confirmModalButton) {
      console.error('Modal buttons not found');
      return;
    }
    
    // 關閉模態框的處理函數
    const handleCloseModal = () => {
      document.body.removeChild(modal);
    };
    
    // 添加事件監聽器
    closeModalButton.addEventListener('click', handleCloseModal);
    confirmModalButton.addEventListener('click', handleCloseModal);
  }

  // 顯示進階設定模態框
  function showAdvancedSettingsModal() {
    // 檢查當前主題
    const isLightMode = document.body.classList.contains('light-mode');

    // 創建模態框元素
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 flex items-center justify-center z-50';
    modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';

    // 創建模態框內容容器
    const modalContent = document.createElement('div');
    modalContent.className = `${isLightMode ? 'bg-white' : 'bg-darker-gray'} rounded-lg max-w-4xl w-full mx-4 z-10 shadow-lg max-h-[90vh] overflow-hidden flex flex-col`;

    // 創建標題欄
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center p-6 border-b';
    header.innerHTML = `
      <h3 class="text-xl font-bold ${isLightMode ? 'text-gray-800' : 'text-white'}">進階設定 - 電池檢測參數</h3>
      <button id="close-advanced-modal" class="${isLightMode ? 'text-gray-600 hover:text-gray-800' : 'text-gray-400 hover:text-white'}">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    `;

    // 創建內容區域
    const content = document.createElement('div');
    content.className = 'flex-1 overflow-y-auto p-6';
    content.id = 'advanced-settings-content';

    // 組裝模態框
    modalContent.appendChild(header);
    modalContent.appendChild(content);
    modal.appendChild(modalContent);

    // 添加到文檔
    document.body.appendChild(modal);

    // 創建設定 UI
    const settingsUI = new BatterySettingsUI();
    settingsUI.createSettingsUI(content);

    // 添加關閉事件監聽器
    const closeButton = modal.querySelector('#close-advanced-modal');
    const handleCloseModal = () => {
      document.body.removeChild(modal);
    };

    closeButton?.addEventListener('click', handleCloseModal);

    // 點擊背景關閉模態框
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        handleCloseModal();
      }
    });

    // ESC 鍵關閉模態框
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleCloseModal();
        document.removeEventListener('keydown', handleKeyDown);
      }
    };
    document.addEventListener('keydown', handleKeyDown);
  }
}



// (function() {
//   alert("SettingsView.ts 已載入");
//   // 檢查當前 DOM 狀態
//   if (document.readyState === 'loading') {
//     //console.log("DOM 仍在載入中，添加事件監聽器");
//     window.addEventListener('DOMContentLoaded', initSettings);
//   } else {
//     //console.log("DOM 已載入完成，直接執行初始化");
//     initSettings();
//   }
  
  
// })();