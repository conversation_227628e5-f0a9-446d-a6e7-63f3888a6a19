import { getBatterySettings, getPackVoltageLimit, getCycleCountLimit, getTemperatureLimit, getIsolateHealth, getCellVoltage, getUnbalanceVoltage, getCycleCountLevel1, getCycleCountLevel2, getCycleCountLevel3, getCycleCountLevel4, getCycleCountLevel5, getCycleCountLevel6 } from '../settings';

// 定義 BatteryData 的 TypeScript 型別，與 Rust 的 BatteryData 對應
export interface BatteryData {
    id: number,
    sn: string,  // 用來對應 C 的 const char*
    pack: number,
    mode: number,
    designVoltage: number,
    designCapacity: number,
    remainingCapacity: number,
    fullyChargedCapacity: number,
    relativeStateOfCharged: number,
    absoluteStateOfCharged: number,
    untilFullyCharged: number,
    untilFullyDischarged: number,
    cycle: number,
    voltage: number,
    current: number,
    chargingVoltage: number,
    chargingCurrent: number,
    averageCurrent: number,
    temperature: number,
    manufactureDate: string,
    gaugeStatus: number,
    error: number,
    /* Extended Data (XXL) */
    cycleIndex: number,
    cycleThreshold: number,
    fullyChargedDate: string,
    fullyChargedCapacityThreshold: number,
    fullyChargedCapacityBackup: number,
    xxlLifetimeMaxPackVoltage: number,
    xxlLifetimeMinPackVoltage: number,
    xxlLifetimeMaxCurrent: number,
    xxlLifetimeMinCurrent: number,
    orangeLED: number,
    fullyChargedVoltage: number,
    firstUseDate: string,
    recordDate: string,
    recordTime: string,
    packMode: number,
    diagParamPfFlag: boolean,
    /* Extended Data */
    cellVoltage_1: number,
    cellVoltage_2: number,
    cellVoltage_3: number,
    cellVoltage_4: number,
    packVoltage: number,
    fetControl: number,
    safetyAlert_1: number,
    safetyAlert_2: number,
    safetyStatus_1: number,
    safetyStatus_2: number,
    pfAlert_1: number,
    pfAlert_2: number,
    pfStatus_1: number,
    pfStatus_2: number,
    operationStatus: number,
    chargingStatus: number,
    temperatureRange: number,
    maxError: number,
    deviceName: string,
    //maxCellVoltage: number,
    //minCellVoltage: number,
    /* Manufacture Block */
    manufactureBlock_1: string,
    manufactureBlock_2: string,
    manufactureBlock_3: string,
    manufactureBlock_4: string,
    /* Lifetime Data */
    lifetimeMaxTemperature: number,
    lifetimeMinTemperature: number,
    lifetimeAvgTemperature: number,
    lifetimeMaxCellVoltage: number,
    lifetimeMinCellVoltage: number,
    lifetimeMaxPackVoltage: number,
    lifetimeMinPackVoltage: number,
    lifetimeMaxChargingCurrent: number,
    lifetimeMaxDischargingCurrent: number,
    lifetimeMaxAvgDischargingCurrent: number,
    lifetimeMaxChargingPower: number,
    lifetimeMaxDischargingPower: number,
    lifetimeMaxAvgDischargingPower: number,
    lifetimeTemperatureSamples: number,
    otEventCount: number,
    otEventDuration: number,
    ovEventCount: number,
    ovEventDuration: number,
}

// interface BatteryDataWithId extends BatteryData {
//     isDamaged: boolean, // 是否損壞;
// }

enum DiagnosisTips
{
    Unknown         = -1,
    SerialNumber    = 0,
    PackVoltage,
    CycleCount,
    Health,
    CellVoltage,
    Unbalance,
    Temperature,
    PFFail,
}

enum DevicePidVid
{
    UPower22    = 0x03EB0022,
    UPower22GD  = 0x03EB4732,
    UPower43    = 0x04830025,
    UPower62    = 0x03EB2402,
    UPower62Wifi= 0x03EB4736,
    UPowerM62   = 0x04830024,
    OPM_P09C    = 0x03EB0023,
    OPM_P14C    = 0x03EB0014,
}

enum BatteryType
{
    Unknown = -1,
    Standard = 0,
    XL,
    XXL,
    Mini,
}

export class batteryDataHelper {
    /**
     * 根據序列號獲取電池類型
     * @param serialNumber 電池序列號
     * @returns 電池類型
     */
    static getBatteryType(serialNumber: string): BatteryType {
        if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
            return BatteryType.Standard;
        } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
            return BatteryType.XL;
        } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
            return BatteryType.XXL;
        } else if (serialNumber.toUpperCase().includes("OMD")) {
            return BatteryType.Mini;
        } else {
            return BatteryType.Unknown;
        }
    }

    /**
     * 根據序列號獲取電池類型文字
     * @param serialNumber 電池序列號
     * @returns 電池類型文字
     */
    static getBatteryTypeText(serialNumber: string): string {
      if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
        return "Standard";
      } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
        return "XL";
      } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
        return "XXL";
      } else if (serialNumber.toUpperCase().includes("OMD")) {
        return "Mini";
      } else {
        return "Unknown";
      }
    }

    
    /**
     * 計算電池健康狀況
     * @param m_FullChargeCapacity 完全充電容量
     * @param m_DesignCapacity 設計容量
     * @returns 電池健康狀況百分比
     */
    static getBatteryHealth(m_FullChargeCapacity: number, m_DesignCapacity: number): number
    {
        try
        {
            const fHealth = (m_FullChargeCapacity / m_DesignCapacity) * 100;

            return (fHealth > 100) ? 100 : fHealth;
        }
        catch (error)
        {
            console.error("計算電池健康狀況時發生錯誤:", error);
            return 0;
        }
    }
    /**
     * 根據電池數據計算循環次數等級
     * @param batteryData 電池數據
     * @returns 循環次數等級
     */
    static getCycleCountLevel(batteryData: BatteryData): number
    {
        /*
            CycleCountLevel1 = 89.55;
            CycleCountLevel2 = 79.80;
            CycleCountLevel3 = 78.56;
            CycleCountLevel4 = 68.64;
            CycleCountLevel5 = 48.64;
            CycleCountLevel6 = 33.00;
        */
        let fLevel = 0;

        try
        {
            if (batteryData != null)
            {
                //if (batteryData.cycle >= getCycleCountLevel(1)) return 1;

                if (batteryData.cycle >= 0 && batteryData.cycle <= 100)
                    fLevel = getCycleCountLevel1(); // 89.55; //CycleCountLevel1
                else if (batteryData.cycle > 101 && batteryData.cycle <= 200)
                    fLevel = getCycleCountLevel2(); //79.80; //CycleCountLevel2
                else if (batteryData.cycle > 201 && batteryData.cycle <= 300)
                    fLevel = getCycleCountLevel3(); //78.56; //CycleCountLevel3
                else if (batteryData.cycle > 301 && batteryData.cycle <= 400)
                    fLevel = getCycleCountLevel4(); //68.64; //CycleCountLevel4
                else if (batteryData.cycle > 401 && batteryData.cycle <= 500)
                    fLevel = getCycleCountLevel5(); //48.64; //CycleCountLevel5
                else if (batteryData.cycle > 501)
                    fLevel = getCycleCountLevel6(); //3.00; //CycleCountLevel6
            }

        }
        catch (error)
        {
            console.error("計算循環次數等級時發生錯誤:", error);
            return 0;
        }
        return fLevel;
    }
}


export { DiagnosisTips, DevicePidVid, BatteryType };