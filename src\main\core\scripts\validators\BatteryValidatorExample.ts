import { BatteryValidator, BatteryDamageInfo } from './BatteryValidator';
import { BatteryData, batteryDataHelper } from '../interfaces/batteryData';

/**
 * BatteryValidator 使用範例
 * 展示如何在實際項目中使用電池驗證器
 */

/**
 * 驗證電池數據中的序列號
 * @param batteryData 電池數據
 * @returns 驗證結果
 */
export function validateBatteryData(batteryData: BatteryData): {
    isValid: boolean;
    batteryType: string;
    dateInSerial: string;
    isExcludedPrefix: boolean;
    validationMessage: string;
} {
    const serial = batteryData.sn;
    
    // 基本驗證
    const isValid = BatteryValidator.isSerialNumberValid(serial);
    const batteryType = batteryDataHelper.getBatteryTypeText(serial);
    const dateInSerial = BatteryValidator.extractDateFromSerial(serial);
    const isExcludedPrefix = BatteryValidator.isExcludedPrefix(dateInSerial);
    
    // 生成驗證訊息
    let validationMessage = "";
    
    if (!BatteryValidator.isSerialFormatValid(serial)) {
        validationMessage = "Serial number format is invalid. Expected format: [prefix]-[date]";
    } else if (batteryType === "Unknown") {
        validationMessage = "Unknown battery type. Serial should start with '9789B' (XL) or '17500' (Standard)";
    } else if (isExcludedPrefix) {
        validationMessage = `Serial contains excluded date: ${dateInSerial}`;
    } else if (isValid) {
        validationMessage = `Valid ${batteryType} battery serial number`;
    } else {
        validationMessage = "Serial number validation failed";
    }
    
    return {
        isValid,
        batteryType,
        dateInSerial,
        isExcludedPrefix,
        validationMessage
    };
}



/**
 * 批量驗證電池數據列表
 * @param batteryDataList 電池數據列表
 * @returns 驗證結果列表
 */
export function validateBatteryDataList(batteryDataList: BatteryData[]): Array<{
    batteryId: number;
    serial: string;
    isValid: boolean;
    batteryType: string;
    validationMessage: string;
}> {
    return batteryDataList.map(batteryData => {
        const validation = validateBatteryData(batteryData);
        return {
            batteryId: batteryData.id,
            serial: batteryData.sn,
            isValid: validation.isValid,
            batteryType: validation.batteryType,
            validationMessage: validation.validationMessage
        };
    });
}

/**
 * 過濾出有效的電池
 * @param batteryDataList 電池數據列表
 * @returns 有效的電池數據列表
 */
export function filterValidBatteries(batteryDataList: BatteryData[]): BatteryData[] {
    return batteryDataList.filter(batteryData => 
        BatteryValidator.isSerialNumberValid(batteryData.sn)
    );
}

/**
 * 過濾出無效的電池
 * @param batteryDataList 電池數據列表
 * @returns 無效的電池數據列表
 */
export function filterInvalidBatteries(batteryDataList: BatteryData[]): BatteryData[] {
    return batteryDataList.filter(batteryData => 
        !BatteryValidator.isSerialNumberValid(batteryData.sn)
    );
}

/**
 * 按電池類型分組
 * @param batteryDataList 電池數據列表
 * @returns 按類型分組的電池數據
 */
export function groupBatteriesByType(batteryDataList: BatteryData[]): {
    XL: BatteryData[];
    Standard: BatteryData[];
    Unknown: BatteryData[];
} {
    const groups = {
        XL: [] as BatteryData[],
        Standard: [] as BatteryData[],
        Unknown: [] as BatteryData[]
    };
    
    batteryDataList.forEach(batteryData => {
        const type = batteryDataHelper.getBatteryTypeText(batteryData.sn);
        if (type === "XL") {
            groups.XL.push(batteryData);
        } else if (type === "Standard") {
            groups.Standard.push(batteryData);
        } else {
            groups.Unknown.push(batteryData);
        }
    });
    
    return groups;
}

/**
 * 生成電池驗證報告
 * @param batteryDataList 電池數據列表
 * @returns 驗證報告
 */
export function generateBatteryValidationReport(batteryDataList: BatteryData[]): {
    totalBatteries: number;
    validBatteries: number;
    invalidBatteries: number;
    batteryTypes: {
        XL: number;
        Standard: number;
        Unknown: number;
    };
    excludedDateBatteries: number;
    validationDetails: Array<{
        batteryId: number;
        serial: string;
        isValid: boolean;
        batteryType: string;
        validationMessage: string;
    }>;
} {
    const validationResults = validateBatteryDataList(batteryDataList);
    const groupedBatteries = groupBatteriesByType(batteryDataList);
    
    const validBatteries = validationResults.filter(r => r.isValid).length;
    const invalidBatteries = validationResults.filter(r => !r.isValid).length;
    
    const excludedDateBatteries = batteryDataList.filter(batteryData => {
        const dateInSerial = BatteryValidator.extractDateFromSerial(batteryData.sn);
        return BatteryValidator.isExcludedPrefix(dateInSerial);
    }).length;
    
    return {
        totalBatteries: batteryDataList.length,
        validBatteries,
        invalidBatteries,
        batteryTypes: {
            XL: groupedBatteries.XL.length,
            Standard: groupedBatteries.Standard.length,
            Unknown: groupedBatteries.Unknown.length
        },
        excludedDateBatteries,
        validationDetails: validationResults
    };
}

/**
 * 在控制台輸出驗證報告
 * @param batteryDataList 電池數據列表
 */
export function printBatteryValidationReport(batteryDataList: BatteryData[]): void {
    const report = generateBatteryValidationReport(batteryDataList);

    console.log("=== Battery Validation Report ===");
    console.log(`Total Batteries: ${report.totalBatteries}`);
    console.log(`Valid Batteries: ${report.validBatteries}`);
    console.log(`Invalid Batteries: ${report.invalidBatteries}`);
    console.log(`Success Rate: ${((report.validBatteries / report.totalBatteries) * 100).toFixed(1)}%`);
    console.log("");
    console.log("Battery Types:");
    console.log(`  XL: ${report.batteryTypes.XL}`);
    console.log(`  Standard: ${report.batteryTypes.Standard}`);
    console.log(`  Unknown: ${report.batteryTypes.Unknown}`);
    console.log("");
    console.log(`Batteries with Excluded Dates: ${report.excludedDateBatteries}`);
    console.log("");
    console.log("Validation Details:");
    report.validationDetails.forEach(detail => {
        const status = detail.isValid ? "✅" : "❌";
        console.log(`  ${status} Battery ${detail.batteryId} (${detail.serial}): ${detail.validationMessage}`);
    });
    console.log("=== End Report ===");
}

/**
 * 電池損壞檢測相關功能
 */

/**
 * 檢查電池是否損壞
 * @param batteryId 電池 ID
 * @returns 是否損壞
 */
export function checkBatteryDamage(batteryId: string): boolean {
    return BatteryValidator.isAppearanceBroken(batteryId);
}

/**
 * 設定電池損壞狀態
 * @param batteryId 電池 ID
 * @param isDamaged 是否損壞
 */
export function setBatteryDamageStatus(batteryId: string, isDamaged: boolean): void {
    BatteryValidator.setBatteryDamage(batteryId, isDamaged);
}

/**
 * 批量檢查多個電池的損壞狀態
 * @param batteryIds 電池 ID 陣列
 * @returns 電池損壞狀態對應表
 */
export function checkMultipleBatteryDamage(batteryIds: string[]): BatteryDamageInfo {
    return BatteryValidator.getBatteryDamageStatus(batteryIds);
}

/**
 * 獲取所有損壞的電池 ID
 * @returns 損壞電池的 ID 陣列
 */
export function getAllDamagedBatteryIds(): string[] {
    return BatteryValidator.getDamagedBatteryIds();
}

/**
 * 生成電池損壞檢測報告
 * @param batteryDataList 電池數據列表
 * @returns 損壞檢測報告
 */
export function generateBatteryDamageReport(batteryDataList: BatteryData[]): {
    totalBatteries: number;
    damagedBatteries: number;
    healthyBatteries: number;
    damageRate: number;
    damagedBatteryDetails: Array<{
        batteryId: number;
        serial: string;
        batteryType: string;
        isDamaged: boolean;
    }>;
} {
    const batteryIds = batteryDataList.map(battery => battery.id.toString());
    const damageStatus = BatteryValidator.getBatteryDamageStatus(batteryIds);

    let damagedCount = 0;
    const damagedBatteryDetails = batteryDataList.map(battery => {
        const isDamaged = damageStatus[battery.id.toString()] || false;
        if (isDamaged) damagedCount++;

        return {
            batteryId: battery.id,
            serial: battery.sn,
            batteryType: batteryDataHelper.getBatteryTypeText(battery.sn),
            isDamaged
        };
    });

    return {
        totalBatteries: batteryDataList.length,
        damagedBatteries: damagedCount,
        healthyBatteries: batteryDataList.length - damagedCount,
        damageRate: batteryDataList.length > 0 ? (damagedCount / batteryDataList.length) * 100 : 0,
        damagedBatteryDetails
    };
}

/**
 * 在控制台輸出電池損壞檢測報告
 * @param batteryDataList 電池數據列表
 */
export function printBatteryDamageReport(batteryDataList: BatteryData[]): void {
    const report = generateBatteryDamageReport(batteryDataList);

    console.log("=== Battery Damage Detection Report ===");
    console.log(`Total Batteries: ${report.totalBatteries}`);
    console.log(`Damaged Batteries: ${report.damagedBatteries}`);
    console.log(`Healthy Batteries: ${report.healthyBatteries}`);
    console.log(`Damage Rate: ${report.damageRate.toFixed(1)}%`);
    console.log("");
    console.log("Battery Damage Details:");
    report.damagedBatteryDetails.forEach(detail => {
        const status = detail.isDamaged ? "🔴 DAMAGED" : "🟢 HEALTHY";
        console.log(`  ${status} Battery ${detail.batteryId} (${detail.serial}) - Type: ${detail.batteryType}`);
    });
    console.log("=== End Damage Report ===");
}

/**
 * 清除所有電池的損壞狀態記錄
 */
export function clearAllBatteryDamageRecords(): void {
    BatteryValidator.clearAllBatteryDamageInfo();
    console.log("所有電池損壞狀態記錄已清除");
}
