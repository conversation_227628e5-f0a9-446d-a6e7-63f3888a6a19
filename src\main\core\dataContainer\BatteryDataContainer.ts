// import { invoke } from "@tauri-apps/api/core";
// import { listen } from '@tauri-apps/api/event';
import { BatteryType } from "../scripts/interfaces/batteryData";

// 定義 BatteryData 的 TypeScript 型別，與 Rust 的 BatteryData 對應
function getBatteryType(serialNumber: string): BatteryType {
    if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
      return BatteryType.Standard; // "Standard";
    } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
      return BatteryType.XL; // "XL";
    } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
      return BatteryType.XXL; // "XXL";
    } else if (serialNumber.toUpperCase().includes("OMD")) {
      return BatteryType.Mini; // "Mini";
    } else {
      return BatteryType.Unknown; // "Unknown";
    }
}

function getBatteryTypeText(serialNumber: string): string {
  if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
    return "Standard";
  } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
    return "XL";
  } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
    return "XXL";
  } else if (serialNumber.toUpperCase().includes("OMD")) {
    return "Mini";
  } else {
    return "Unknown";
  }
}

export {
    getBatteryType,
    getBatteryTypeText,
}