<!-- 統計卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">總電池數</div>
        <div id="total-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-accent-yellow">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M13 7H7v6h6V7z"></path>
          <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7.586 5H7a3 3 0 00-3 3v8a3 3 0 003 3h6a3 3 0 003-3V8a3 3 0 00-3-3h-.586l1.293-1.293A1 1 0 0016 2H7zm6 5a1 1 0 011 1v8a1 1 0 01-1 1H7a1 1 0 01-1-1V8a1 1 0 011-1h6z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">正常電池</div>
        <div id="normal-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-green-500">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">充電中</div>
        <div id="charging-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-blue-500">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M11 3a1 1 0 10-2 0v1.188C7.09 4.938 6 6.7 6 8.5 6 10.433 7.33 12 9 12c.685 0 1.216-.138 1.627-.361.154-.083.34-.17.51-.245.886-.393 1.37-.788 1.784-1.225.415-.437.79-.924 1.067-1.483.276-.56.412-1.17.412-1.822 0-1.638-1.04-2.812-2.4-2.864V3zm-6 5.5c0-2.39 1.5-4.5 4-5.41V2a3 3 0 016 0v1.09c2.5.91 4 3.01 4 5.41 0 2.48-1.5 4.5-4 5.41v1.09a3 3 0 01-6 0v-1.09c-2.5-.91-4-3.01-4-5.41z"></path>
        </svg>
      </div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">低電量</div>
        <div id="low-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-yellow-500">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
  </div>
</div>

<!-- 電池管理文字區塊 -->
<div class="card p-6 mb-6">
  <div class="flex flex-col md:flex-row">
    <div class="md:w-2/3">
      <h2 class="text-2xl font-bold mb-4">Welcome to Onyx UPower Application!</h2>
      <p class="text-sm text-gray-400 mb-4">UPower Application is a powerful tool for managing your business.</p>
      <button class="mt-4 px-4 py-2 bg-yellow-500 text-black rounded font-bold">Get Started</button>
    </div>
    <div class="md:w-1/3">
      <img src="src/main/assets/128x128.png" alt="Onyx Logo" class="h-24 w-24 mx-auto mt-4 md:mt-0">
    </div>
  </div>
</div>

<!-- 電池管理區塊 -->
<div class="card p-6 mb-6">
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-bold">電池管理</h2>
    <!-- <button id="add-battery-btn" class="px-4 py-2 bg-yellow-500 text-black rounded font-bold">新增電池</button> -->
  </div>
  
  <!-- 電池列表容器 -->
  <div id="batteries-container" class="mt-4">
    <!-- 電池列表將由 JavaScript 動態生成 -->
    <div class="text-center p-6">
      <p class="text-gray-400">載入中...</p>
    </div>
  </div>
</div>

<!-- 電池狀態圖表 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
  <div class="card p-6">
    <h3 class="text-lg font-bold mb-4">電池容量分佈</h3>
    <div class="h-64 bg-gray-800 rounded flex items-center justify-center">
      <p class="text-gray-400">電池容量分佈圖表</p>
    </div>
  </div>
  
  <div class="card p-6">
    <h3 class="text-lg font-bold mb-4">電池狀態分佈</h3>
    <div class="h-64 bg-gray-800 rounded flex items-center justify-center">
      <p class="text-gray-400">電池狀態分佈圖表</p>
    </div>
  </div>
</div>


<!-- 載入 BatteryViewLog.ts 腳本 -->
<!-- <script type="module" src="../../../core/scripts/pages/BatteryView.ts" defer></script> -->
<!-- <script type="module" crossorigin src="/src/main/core/scripts/pages/BatteryView.ts" defer></script>
<script>
    (function() {
      // 儀表板頁面的初始化邏輯
      console.log("初始化設定頁面");
      const controller = new BatteryViewController();
      controller.initialize();
    })();
</script> -->