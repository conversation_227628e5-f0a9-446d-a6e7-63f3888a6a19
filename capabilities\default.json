{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "http:default", {"identifier": "http:default", "allow": [{"url": "http://localhost:8080/*"}], "deny": [{"url": "https://private.tauri.app"}]}, "fs:default", {"identifier": "fs:allow-exists", "allow": [{"path": "$APPDATA/*"}]}, {"identifier": "fs:scope", "allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/**"}]}, {"identifier": "fs:allow-rename", "allow": [{"path": "$HOME/**"}]}, {"identifier": "fs:allow-rename", "deny": [{"path": "$HOME/.config/**"}]}, "fs:allow-app-read-recursive", "fs:allow-desktop-read-recursive", "fs:allow-desktop-meta-recursive", "fs:allow-resource-read-recursive", "fs:allow-resource-meta-recursive"]}