# UPower 電池模擬功能使用說明

## 功能概述

我已經為您的 UPowerSDK.rs 添加了完整的電池模擬功能，讓您可以在沒有實際設備的情況下測試 Charger_GetBatteries 函數。

## 新增的功能

### 1. 模擬模式控制
- `set_simulation_mode(enabled: bool)` - 啟用/停用模擬模式
- `is_simulation_mode()` - 檢查當前是否為模擬模式
- `trigger_simulation_update()` - 手動觸發電池狀態更新

### 2. 模擬特性
- **隨機電池插入/拔除**: 6個插槽中隨機有2-4顆電池插入
- **動態狀態更新**: 每5秒自動更新電池狀態，30%機率改變狀態
- **真實數據模擬**: 基於您提供的真實電池數據生成模擬數據
- **隨機變化**: 電量、電壓、溫度等參數會隨機變化

## 使用方法

### 1. 啟用模擬模式
```rust
// 在前端調用
set_simulation_mode(true);
```

### 2. 初始化系統
```rust
// 在模擬模式下，Initial 會返回 true 並設置模擬的 spec
Initial(1234); // 會成功返回 true
```

### 3. 獲取電池數據
```rust
// 現在 Charger_GetBatteries 會返回模擬的電池數據
let batteries = Charger_GetBatteries();
```

### 4. 手動觸發更新（可選）
```rust
// 手動觸發電池狀態變化
trigger_simulation_update();
```

## 模擬數據特點

### 電池狀態模擬
- **插入狀態**: 隨機2-4顆電池插入
- **電池類型**: 隨機選擇 Pack 2 或 Pack 3
- **電量範圍**: 20-100%
- **電壓範圍**: 11000-12600mV
- **溫度範圍**: 20-30°C

### 動態變化
- **插入機率**: 空插槽有60%機率插入電池
- **拔除機率**: 已插入電池有40%機率被拔除
- **更新頻率**: 每5秒自動更新一次
- **變化機率**: 每次更新有30%機率改變狀態

### 數據真實性
- 基於您提供的真實設備數據
- 包含完整的電池參數（電壓、電流、溫度、循環次數等）
- 模擬序列號格式：SIM12345678
- 包含生命週期數據和安全狀態

## 測試流程建議

1. **啟用模擬模式**
   ```rust
   set_simulation_mode(true);
   ```

2. **初始化系統**
   ```rust
   let result = Initial(1234);
   // 應該返回 true
   ```

3. **獲取初始電池狀態**
   ```rust
   let batteries = Charger_GetBatteries();
   // 會看到2-4顆隨機電池
   ```

4. **觀察動態變化**
   - 等待5秒後再次調用 `Charger_GetBatteries()`
   - 或手動調用 `trigger_simulation_update()` 然後獲取數據
   - 觀察電池插入/拔除狀態的變化

5. **停用模擬模式**
   ```rust
   set_simulation_mode(false);
   ```

## 控制台輸出

模擬模式會在控制台輸出詳細信息：
- 模擬模式啟用/停用狀態
- 電池插入/拔除事件
- 電池數據更新
- 每個電池的基本信息（ID、SN、電量）

## 注意事項

1. **模擬模式優先**: 啟用模擬模式後，所有相關函數都會使用模擬數據
2. **數據持久性**: 模擬數據會在模擬模式期間保持，直到手動停用
3. **線程安全**: 使用 Mutex 確保多線程環境下的數據安全
4. **內存管理**: 停用模擬模式時會自動清理模擬數據

## 字符串指針處理

您問到的 `battery_data.ManufactureDate = 0x187e4f1b6f0` 這種寫法，在模擬模式中是這樣處理的：

```rust
// 創建模擬字符串並返回指針
fn create_simulated_string(content: &str) -> *const c_char {
    let c_string = CString::new(content).unwrap_or_else(|_| CString::new("").unwrap());
    let ptr = c_string.as_ptr();
    
    // 將字符串存儲到全局變數中，確保指針有效
    let mut strings = SIMULATED_STRINGS.lock().unwrap();
    strings.push(c_string);
    
    ptr
}

// 使用方式
battery_data.ManufactureDate = create_simulated_string("20241225");
battery_data.SN = create_simulated_string("SIM12345678");
```

這樣做的好處：
1. **指針安全**: 字符串存儲在全局變數中，確保指針始終有效
2. **內存管理**: 自動管理 C 字符串的生命週期
3. **類型正確**: 返回正確的 `*const c_char` 類型指針
4. **數據真實**: 生成的指針地址類似 `0x187e4f1b6f0` 這樣的真實內存地址

這樣您就可以在沒有實際設備的情況下完整測試電池管理功能了！