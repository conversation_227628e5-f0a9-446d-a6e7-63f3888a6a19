# 電池檢測全域設定系統

這個模組提供了一個完整的電池檢測參數管理系統，允許用戶在 Settings 頁面中配置各種檢測標準，並在整個應用程式中使用這些設定。

## 功能特色

- 🔧 **全域設定管理** - 統一管理所有電池檢測參數
- 💾 **持久化儲存** - 使用 localStorage 自動儲存設定
- 🎛️ **圖形化設定介面** - 在 Settings 頁面提供完整的設定 UI
- 🔄 **即時更新** - 設定變更後立即生效
- 📊 **設定驗證** - 確保輸入值在合理範圍內
- 📁 **匯入/匯出** - 支援設定檔案的匯入和匯出
- 🔔 **變更通知** - 設定變更時通知相關模組

## 設定參數

### 基本檢測標準
- `packVoltageLimit`: Pack 電壓限制 (mV) - 預設: 6000
- `cellVoltage`: 單體電壓限制 (mV) - 預設: 2000
- `unbalanceVoltage`: 不平衡電壓限制 (mV) - 預設: 500
- `cycleCountLimit`: 循環次數限制 - 預設: 750
- `temperatureLimit`: 溫度限制 (°C) - 預設: 50

### 循環計數健康度等級
- `cycleCountLevel1-6`: 健康度等級閾值 (%) - 預設: 89.55, 79.80, 78.56, 68.64, 48.64, 33.00

### 電池隔離標準
- `isolateCycle`: 隔離循環次數 - 預設: 750
- `isolateHealth`: 隔離健康度閾值 (%) - 預設: 35
- `isolateYearTime`: 隔離年限 - 預設: 3

### 列表設定
- `locationList`: 可用位置列表
- `brokenReason`: 損壞原因列表

## 使用方法

### 基本使用

```typescript
import { 
    getPackVoltageLimit, 
    getCycleCountLimit, 
    getTemperatureLimit,
    getBatterySettings 
} from '../settings';

// 獲取單個設定值
const voltageLimit = getPackVoltageLimit(); // 6000
const cycleLimit = getCycleCountLimit(); // 750
const tempLimit = getTemperatureLimit(); // 50

// 獲取完整設定物件
const settings = getBatterySettings();
console.log(settings.getSettings());
```

### 在驗證器中使用

```typescript
import { BatteryValidator } from '../validators/BatteryValidator';

// 使用全域設定進行檢測
const isVoltageOk = BatteryValidator.isVoltageNormal(batteryData.voltage);
const isTempOk = BatteryValidator.isTemperatureNormal(batteryData.temperature);
const healthLevel = BatteryValidator.getBatteryHealthLevel(healthPercentage);

// 綜合檢測
const checkResult = BatteryValidator.comprehensiveBatteryCheck(batteryData);
```

### 在 DiagnosticsView 中使用

```typescript
import { DiagnosticsWithSettings } from '../settings';

// 檢測電池合規性
const compliance = DiagnosticsWithSettings.checkBatteryCompliance(batteryData);
if (!compliance.isCompliant) {
    console.log("問題:", compliance.issues);
    console.log("建議:", compliance.recommendations);
}

// 生成狀態報告
DiagnosticsWithSettings.generateStatusReport(batteryDataList);
```

### 在 verify 功能中使用

```typescript
import { VerifyWithSettings } from '../settings';

// 驗證電池可用性
const verification = VerifyWithSettings.verifyBatteryUsability(batteryData);
if (verification.canUse) {
    console.log("建議位置:", verification.suggestedLocation);
} else {
    console.log("不可使用原因:", verification.reason);
}

// 批量驗證
const result = VerifyWithSettings.batchVerifyBatteries(batteryDataList);
console.log("可用電池:", result.usable.length);
console.log("不可用電池:", result.unusable.length);
```

## Settings 頁面整合

### 開啟進階設定

在 Settings 頁面中，點擊「進階設定」按鈕即可開啟電池檢測參數設定介面。

### 設定介面功能

1. **數值設定** - 直接修改各種檢測參數
2. **列表管理** - 新增/移除位置和損壞原因
3. **儲存設定** - 點擊儲存按鈕套用變更
4. **重置設定** - 恢復為預設值
5. **匯出設定** - 下載設定檔案
6. **匯入設定** - 從檔案載入設定

## 設定變更監聽

```typescript
import { SettingsMonitor } from '../settings';

// 啟動設定監聽器
const monitor = SettingsMonitor.getInstance();

// 監聽設定變更事件
document.addEventListener('batterySettingsUpdated', (event) => {
    const detail = event.detail;
    console.log(`${detail.module} 模組收到設定更新通知`);
    
    // 重新執行相關檢測或更新 UI
    if (detail.module === 'diagnostics') {
        // 更新診斷頁面
    } else if (detail.module === 'verify') {
        // 更新驗證功能
    }
});
```

## 設定檔案格式

匯出的設定檔案為 JSON 格式：

```json
{
    "packVoltageLimit": 6000,
    "cycleCountLevel1": 89.55,
    "cycleCountLevel2": 79.80,
    "cycleCountLevel3": 78.56,
    "cycleCountLevel4": 68.64,
    "cycleCountLevel5": 48.64,
    "cycleCountLevel6": 33.00,
    "cellVoltage": 2000,
    "unbalanceVoltage": 500,
    "cycleCountLimit": 750,
    "temperatureLimit": 50,
    "isolateCycle": 750,
    "isolateHealth": 35,
    "isolateYearTime": 3,
    "locationList": [
        "Storage Area A",
        "Storage Area B",
        "Maintenance Zone",
        "Testing Lab",
        "Shipping Dock"
    ],
    "brokenReason": [
        "Physical Damage",
        "Overheating",
        "Voltage Drop",
        "Cycle Limit Exceeded",
        "Manufacturing Defect",
        "Age Related Degradation",
        "Unbalanced Cells"
    ]
}
```

## API 參考

### BatterySettings 類

- `getInstance()`: 獲取單例實例
- `getSettings()`: 獲取當前設定
- `updateSettings(settings)`: 更新設定
- `resetToDefaults()`: 重置為預設值
- `exportSettings()`: 匯出設定為 JSON
- `importSettings(json)`: 從 JSON 匯入設定
- `addListener(callback)`: 添加變更監聽器

### 便捷存取函數

- `getPackVoltageLimit()`: 獲取 Pack 電壓限制
- `getCycleCountLimit()`: 獲取循環次數限制
- `getTemperatureLimit()`: 獲取溫度限制
- `getCellVoltage()`: 獲取單體電壓限制
- `getUnbalanceVoltage()`: 獲取不平衡電壓限制
- `getCycleCountLevel(level)`: 獲取指定等級的健康度閾值
- `getLocationList()`: 獲取位置列表
- `getBrokenReasonList()`: 獲取損壞原因列表

## 注意事項

1. **設定驗證** - 所有數值輸入都會進行範圍驗證
2. **即時生效** - 設定變更後立即儲存並生效
3. **備份建議** - 建議定期匯出設定檔案作為備份
4. **權限管理** - 進階設定功能需要適當的使用者權限
5. **相容性** - 匯入設定時會自動合併新增的設定項目

## 更新日誌

- v1.0.0: 初始版本，包含完整的設定管理功能
- 支援所有基本檢測參數的配置
- 提供完整的 UI 介面和 API
- 整合到 Settings 頁面和驗證器系統
