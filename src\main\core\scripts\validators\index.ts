/**
 * 驗證器模組導出文件
 * 統一導出所有驗證相關的類和函數
 */

// 導出主要的驗證器類和介面
export { BatteryValidator } from './BatteryValidator';
export type { BatteryDamageInfo } from './BatteryValidator';

// 導出序列號驗證相關函數
export {
    validateBatteryData,
    validateBatteryDataList,
    filterValidBatteries,
    filterInvalidBatteries,
    groupBatteriesByType,
    generateBatteryValidationReport,
    printBatteryValidationReport
} from './BatteryValidatorExample';

// 導出電池損壞檢測相關函數
export {
    checkBatteryDamage,
    setBatteryDamageStatus,
    checkMultipleBatteryDamage,
    getAllDamagedBatteryIds,
    generateBatteryDamageReport,
    printBatteryDamageReport,
    clearAllBatteryDamageRecords
} from './BatteryValidatorExample';

// 導出測試函數（可選，用於開發和調試）
export {
    runBatteryValidatorTests,
    runHelperMethodsTests
} from './BatteryValidator.test';
