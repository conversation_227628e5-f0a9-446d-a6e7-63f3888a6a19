// import { invoke } from "@tauri-apps/api/core";
// import { listen } from '@tauri-apps/api/event';
import { fetch } from '@tauri-apps/plugin-http'


console.log("BatteryLogView.ts 已載入");

// 電池資料介面
interface Battery {
  id: string;
  name: string;
  status: string;
  capacity: number;
  last_charged: string;
}
// 定義 BatteryData 的 TypeScript 型別，與 Rust 的 BatteryData 對應
// interface BatteryData {
//   id: number;
//   sn: string;
// }

// API 服務
class BatteryApiService {
  private apiUrl = 'http://localhost:8080/api';

  // 獲取所有電池
  async getAllBatteries(): Promise<Battery[]> {     
    try {
      const response = await fetch(`${this.apiUrl}/batteries`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("獲取電池列表失敗:", error);
      return [];
    }
  }

  // 獲取單個電池
  async getBattery(id: string): Promise<Battery | null> {
    try {
      const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error(`獲取電池 ${id} 失敗:`, error);
      return null;
    }
  }

  // 新增電池
  async addBattery(battery: Omit<Battery, "id">): Promise<Battery | null> {
    try {
      console.log("battery", battery);
      const response = await fetch(`${this.apiUrl}/batteries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(battery)
      });
      console.log("response", response);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("新增電池失敗:", error);
      return null;
    }
  }

  // 修改電池
  async updateBattery(id: string, battery: Partial<Battery>): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(battery)
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return true;
    } catch (error) {
      console.error(`修改電池 ${id} 失敗:`, error);
      return false;
    }
  }

  // 刪除電池
  async deleteBattery(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return true;
    } catch (error) {
      console.error(`刪除電池 ${id} 失敗:`, error);
      return false;
    }
  }
}

// 電池管理
export class BatteryLogViewController {
  private apiService: BatteryApiService;
  private batteriesContainer: HTMLElement | null;
  
  constructor() {
    this.apiService = new BatteryApiService();
    this.batteriesContainer = document.getElementById("batteries-container");
  }

  // 初始化
  async initialize() {
    await this.loadBatteries();
    this.setupEventListeners();
  }

  // 讀取電池列表
  async loadBatteries() {
    if (!this.batteriesContainer) 
      {
        console.error("Element batteries-container not found");
        return
      };
    const batteries = await this.apiService.getAllBatteries();
    console.log("batteries", batteries);
    // 更新統計卡片
    this.updateStatistics(batteries);
    
    if (batteries.length === 0) {
      this.batteriesContainer.innerHTML = `
        <div class="text-center p-6">
          <p class="text-gray-400">尚無電池資料</p>
          <button id="add-battery-btn" class="mt-4 px-4 py-2 bg-yellow-500 text-black rounded font-bold">
            新增電池
          </button>
        </div>
      `;
      return;
    }
    
    this.renderBatteries(batteries);
  }

  // 組合電池列表 HTML
  renderBatteries(batteries: Battery[]) {
    if (!this.batteriesContainer) return;
    
    let html = `
      <div class="flex justify-between mb-4">
        <h3 class="text-lg font-bold">電池列表</h3>
        <button id="add-battery-btn" onclick="showBatteryForm()" class="px-3 py-1 bg-yellow-500 text-black rounded text-sm">
          新增電池
        </button>
      </div>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="text-left text-gray-400 text-sm">
              <th class="pb-3">ID</th>
              <th class="pb-3">名稱</th>
              <th class="pb-3">狀態</th>
              <th class="pb-3">容量</th>
              <th class="pb-3">最後充電</th>
              <th class="pb-3">操作</th>
            </tr>
          </thead>
          <tbody class="text-sm">
    `;
    
    batteries.forEach(battery => {
      html += `
        <tr class="border-t border-gray-700" data-id="${battery.id}">
          <td class="py-3">${battery.id}</td>
          <td class="py-3">${battery.name}</td>
          <td class="py-3">${this.getStatusBadge(battery.status)}</td>
          <td class="py-3">${battery.capacity}%</td>
          <td class="py-3">${battery.last_charged}</td>
          <td class="py-3">
            <button class="edit-btn px-2 py-1 bg-blue-600 text-white rounded-sm mr-2">編輯</button>
            <button class="delete-btn px-2 py-1 bg-red-600 text-white rounded-sm">刪除</button>
          </td>
        </tr>
      `;
    });
    
    html += `
          </tbody>
        </table>
      </div>
    `;
    
    this.batteriesContainer.innerHTML = html;
  }

  // 獲取狀態標籤
  getStatusBadge(status: string): string {
    const statusMap: Record<string, { bg: string, text: string }> = {
      'normal': { bg: 'bg-green-900', text: 'text-green-300' },
      'charging': { bg: 'bg-blue-900', text: 'text-blue-300' },
      'low': { bg: 'bg-yellow-900', text: 'text-yellow-300' },
      'critical': { bg: 'bg-red-900', text: 'text-red-300' }
    };

    const statusNameMap: Record<string, { zh: string }> = {
      'normal': { zh: '正常' },
      'charging': { zh: '充電中' },
      'low': { zh: '低電量' },
      'critical': { zh: '電量危急' }
    };
    
    const style = statusMap[status.toLowerCase()] || { bg: 'bg-gray-900', text: 'text-gray-300', zh: 'null' };
    const name = statusNameMap[status.toLowerCase()] || { zh: status };
    return `<span class="px-2 py-1 ${style.bg} ${style.text} rounded-full text-xs">${name.zh}</span>`;
  }

  // 設置事件監聽器
  setupEventListeners() {
    if (!this.batteriesContainer) return;
    
    // 新增電池按鈕
    const addBtn = document.getElementById("add-battery-btn");
    if (addBtn) {
      addBtn.addEventListener("click", () => this.showBatteryForm());
    }
    
    // 編輯和刪除按鈕
    this.batteriesContainer.addEventListener("click", (e) => {
      const target = e.target as HTMLElement;
      const row = target.closest("tr");
      if (!row) return;
      
      const batteryId = row.getAttribute("data-id");
      if (!batteryId) return;
      
      if (target.classList.contains("edit-btn")) {
        this.showBatteryForm(batteryId);
      } else if (target.classList.contains("delete-btn")) {
        this.confirmDeleteBattery(batteryId);
      }
    });
  }

  // 新增、修改電池表單
  async showBatteryForm(batteryId?: string) {
    alert(':showBatteryForm');
    let battery: Battery | null = null;
    
    if (batteryId) {
      battery = await this.apiService.getBattery(batteryId);
      if (!battery) return;
    }
    
    const modal = document.createElement("div");
    modal.className = "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
    modal.innerHTML = `
      <div class="bg-gray-800 p-6 rounded-lg w-full max-w-md">
        <h3 class="text-lg font-bold mb-4">${batteryId ? '編輯電池' : '新增電池'}</h3>
        <form id="battery-form">
          ${batteryId ? `<input type="hidden" id="battery-id" value="${batteryId}">` : ''}
          <div class="mb-4">
            <label class="block text-sm text-gray-400 mb-1">名稱</label>
            <input type="text" id="battery-name" class="w-full p-2 bg-gray-700 rounded" value="${battery?.name || ''}">
          </div>
          <div class="mb-4">
            <label class="block text-sm text-gray-400 mb-1">狀態</label>
            <select id="battery-status" class="w-full p-2 bg-gray-700 rounded">
              <option value="normal" ${battery?.status === 'normal' ? 'selected' : ''}>正常</option>
              <option value="charging" ${battery?.status === 'charging' ? 'selected' : ''}>充電中</option>
              <option value="low" ${battery?.status === 'low' ? 'selected' : ''}>低電量</option>
              <option value="critical" ${battery?.status === 'critical' ? 'selected' : ''}>電量危急</option>
            </select>
          </div>
          <div class="mb-4">
            <label class="block text-sm text-gray-400 mb-1">容量 (%)</label>
            <input type="number" id="battery-capacity" min="0" max="100" class="w-full p-2 bg-gray-700 rounded" value="${battery?.capacity || 100}">
          </div>
          <div class="mb-4">
            <label class="block text-sm text-gray-400 mb-1">最後充電時間</label>
            <input type="datetime-local" id="battery-last-charged" class="w-full p-2 bg-gray-700 rounded" value="${battery?.last_charged ? battery.last_charged.replace(' ', 'T') : new Date().toISOString().slice(0, 16)}">
          </div>
          <div class="flex justify-end space-x-2">
            <button type="button" id="cancel-form" class="px-4 py-2 bg-gray-600 rounded">取消</button>
            <button type="submit" class="px-4 py-2 bg-yellow-500 text-black rounded font-bold">儲存</button>
          </div>
        </form>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // 表單取消按鈕
    const cancelBtn = document.getElementById("cancel-form");
    if (cancelBtn) {
      cancelBtn.addEventListener("click", () => {
        document.body.removeChild(modal);
      });
    }
    
    // 表單提交
    const form = document.getElementById("battery-form") as HTMLFormElement;
    if (form) {
      form.addEventListener("submit", async (e) => {
        e.preventDefault();
        
        const nameInput = document.getElementById("battery-name") as HTMLInputElement;
        const statusSelect = document.getElementById("battery-status") as HTMLSelectElement;
        const capacityInput = document.getElementById("battery-capacity") as HTMLInputElement;
        const lastChargedInput = document.getElementById("battery-last-charged") as HTMLInputElement;
        
        const batteryData = {
          name: nameInput.value,
          status: statusSelect.value,
          capacity: parseInt(capacityInput.value),
          last_charged: lastChargedInput.value.replace('T', ' ')
        };
        
        let success = false;
        
        if (batteryId) {
          // 更新電池
          success = await this.apiService.updateBattery(batteryId, batteryData);
        } else {
          // 新增電池
          const newBattery = await this.apiService.addBattery(batteryData);
          success = !!newBattery;
        }
        
        if (success) {
          document.body.removeChild(modal);
          await this.loadBatteries();
        } else {
          alert("操作失敗，請稍後再試");
        }
      });
    }
  }

  // 確認刪除電池
  async confirmDeleteBattery(batteryId: string) {
    if (confirm("確定要刪除此電池嗎？此操作無法復原。")) {
      const success = await this.apiService.deleteBattery(batteryId);
      if (success) {
        await this.loadBatteries();
      } else {
        alert("刪除失敗，請稍後再試");
      }
    }
  }

  // 更新統計卡片
  updateStatistics(batteries: Battery[]) {
    const totalElement = document.getElementById("total-batteries");
    const normalElement = document.getElementById("normal-batteries");
    const chargingElement = document.getElementById("charging-batteries");
    const lowElement = document.getElementById("low-batteries");
    
    if (totalElement) totalElement.textContent = batteries.length.toString();
    
    // 計算各種狀態的電池數量
    const normalCount = batteries.filter(b => b.status.toLowerCase() === 'normal').length;
    const chargingCount = batteries.filter(b => b.status.toLowerCase() === 'charging').length;
    const lowCount = batteries.filter(b => b.status.toLowerCase() === 'low' || b.status.toLowerCase() === 'critical').length;
    
    if (normalElement) normalElement.textContent = normalCount.toString();
    if (chargingElement) chargingElement.textContent = chargingCount.toString();
    if (lowElement) lowElement.textContent = lowCount.toString();
  }
}