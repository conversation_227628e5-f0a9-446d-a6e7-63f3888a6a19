<link href="/src/main/res/styles/pages/view_settings.css" rel="stylesheet">

<!-- 系統設定頁面 -->
<div class="card p-6 mb-6">
  <h2 class="text-xl font-bold mb-4">系統設定</h2>
  
  <!-- 外觀設定 -->
  <div class="mb-6">
    <h3 class="text-lg font-semibold mb-3">外觀設定</h3>
    <div class="info-card bg-gray-800 p-4 rounded">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-medium">顯示模式</p>
          <p class="text-sm text-gray-400">切換應用程式的顯示模式</p>
        </div>
        <div class="flex items-center">
          <button id="theme-toggle" class="theme-button p-2 rounded-full bg-accent-yellow text-dark-gray">
            <svg id="theme-icon" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </button>
          <span id="theme-text" class="ml-2">暗色模式</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 進階設定 -->
  <div class="mb-6">
    <h3 class="text-lg font-semibold mb-3">進階設定</h3>
    <div class="info-card bg-gray-800 p-4 rounded">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-medium">電池檢測設定</p>
          <p class="text-sm text-gray-400">配置電池檢測標準和參數</p>
        </div>
        <button id="advanced-settings-button" class="p-2 rounded-full bg-gray-700 text-white">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- 幫助與支援 -->
  <div>
    <h3 class="text-lg font-semibold mb-3">幫助與支援</h3>
    <div class="info-card bg-gray-800 p-4 rounded">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-medium">使用指南</p>
          <p class="text-sm text-gray-400">查看應用程式的使用說明</p>
        </div>
        <button id="help-button" class="p-2 rounded-full bg-gray-700 text-white">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 系統資訊 -->
<div class="card p-6">
  <h3 class="text-lg font-bold mb-4">系統資訊</h3>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="info-card bg-gray-800 p-4 rounded">
      <div class="text-sm text-gray-400">應用版本</div>
      <div class="font-medium">0.25.318</div>
    </div>
    <div class="info-card bg-gray-800 p-4 rounded">
      <div class="text-sm text-gray-400">最後更新</div>
      <div class="font-medium">2023/07/15</div>
    </div>
  </div>
</div>

<!-- 添加主題切換和幫助功能的 JavaScript -->
<!-- <script type="module" src="/src/main/core/scripts/pages/settingsView.ts" defer></script> -->
