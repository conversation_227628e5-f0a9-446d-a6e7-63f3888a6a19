/**
 * 電池檢測設定介面
 */
export interface BatteryDetectionSettings {
    // 電池檢測標準
    packVoltageLimit: number;
    cycleCountLevel1: number;
    cycleCountLevel2: number;
    cycleCountLevel3: number;
    cycleCountLevel4: number;
    cycleCountLevel5: number;
    cycleCountLevel6: number;
    cellVoltage: number;
    unbalanceVoltage: number;
    cycleCountLimit: number;
    temperatureLimit: number;
    
    // 電池隔離標準
    isolateCycle: number;
    isolateHealth: number;
    isolateYearTime: number;
    
    // 列表設定
    locationList: string[];
    brokenReason: string[];
}

/**
 * 預設設定值
 */
const DEFAULT_SETTINGS: BatteryDetectionSettings = {
    // 電池檢測標準
    packVoltageLimit: 6000,
    cycleCountLevel1: 89.55,
    cycleCountLevel2: 79.80,
    cycleCountLevel3: 78.56,
    cycleCountLevel4: 68.64,
    cycleCountLevel5: 48.64,
    cycleCountLevel6: 33.00,
    cellVoltage: 2000,
    unbalanceVoltage: 500,
    cycleCountLimit: 750,
    temperatureLimit: 50,
    
    // 電池隔離標準
    isolateCycle: 750,
    isolateHealth: 35,
    isolateYearTime: 3,
    
    // 列表設定
    locationList: [
        "Storage Area A",
        "Storage Area B", 
        "Maintenance Zone",
        "Testing Lab",
        "Shipping Dock"
    ],
    brokenReason: [
        "Physical Damage",
        "Overheating",
        "Voltage Drop",
        "Cycle Limit Exceeded",
        "Manufacturing Defect",
        "Age Related Degradation",
        "Unbalanced Cells"
    ]
};

/**
 * 電池設定管理類
 */
export class BatterySettings {
    private static readonly SETTINGS_KEY = 'batteryDetectionSettings';
    private static instance: BatterySettings;
    private settings: BatteryDetectionSettings;
    private listeners: Array<(settings: BatteryDetectionSettings) => void> = [];

    private constructor() {
        this.settings = this.loadSettings();
    }

    /**
     * 獲取單例實例
     */
    public static getInstance(): BatterySettings {
        if (!BatterySettings.instance) {
            BatterySettings.instance = new BatterySettings();
        }
        return BatterySettings.instance;
    }

    /**
     * 從 localStorage 載入設定
     */
    private loadSettings(): BatteryDetectionSettings {
        try {
            const stored = localStorage.getItem(BatterySettings.SETTINGS_KEY);
            if (stored) {
                const parsedSettings = JSON.parse(stored);
                // 合併預設設定，確保新增的設定項目有預設值
                return { ...DEFAULT_SETTINGS, ...parsedSettings };
            }
        } catch (error) {
            console.error('載入電池設定失敗:', error);
        }
        return { ...DEFAULT_SETTINGS };
    }

    /**
     * 儲存設定到 localStorage
     */
    private saveSettings(): void {
        try {
            localStorage.setItem(BatterySettings.SETTINGS_KEY, JSON.stringify(this.settings));
            this.notifyListeners();
        } catch (error) {
            console.error('儲存電池設定失敗:', error);
        }
    }

    /**
     * 獲取當前設定
     */
    public getSettings(): BatteryDetectionSettings {
        return { ...this.settings };
    }

    /**
     * 更新設定
     */
    public updateSettings(newSettings: Partial<BatteryDetectionSettings>): void {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
    }

    /**
     * 重置為預設設定
     */
    public resetToDefaults(): void {
        this.settings = { ...DEFAULT_SETTINGS };
        this.saveSettings();
    }

    /**
     * 獲取特定設定值
     */
    public get packVoltageLimit(): number { return this.settings.packVoltageLimit; }
    public get cycleCountLevel1(): number { return this.settings.cycleCountLevel1; }
    public get cycleCountLevel2(): number { return this.settings.cycleCountLevel2; }
    public get cycleCountLevel3(): number { return this.settings.cycleCountLevel3; }
    public get cycleCountLevel4(): number { return this.settings.cycleCountLevel4; }
    public get cycleCountLevel5(): number { return this.settings.cycleCountLevel5; }
    public get cycleCountLevel6(): number { return this.settings.cycleCountLevel6; }
    public get cellVoltage(): number { return this.settings.cellVoltage; }
    public get unbalanceVoltage(): number { return this.settings.unbalanceVoltage; }
    public get cycleCountLimit(): number { return this.settings.cycleCountLimit; }
    public get temperatureLimit(): number { return this.settings.temperatureLimit; }
    public get isolateCycle(): number { return this.settings.isolateCycle; }
    public get isolateHealth(): number { return this.settings.isolateHealth; }
    public get isolateYearTime(): number { return this.settings.isolateYearTime; }
    public get locationList(): string[] { return [...this.settings.locationList]; }
    public get brokenReason(): string[] { return [...this.settings.brokenReason]; }

    /**
     * 設定特定值
     */
    public setPackVoltageLimit(value: number): void {
        if (this.validateNumericValue(value, 1000, 10000)) {
            this.updateSettings({ packVoltageLimit: value });
        }
    }

    public setCycleCountLevel(level: 1|2|3|4|5|6, value: number): void {
        if (this.validateNumericValue(value, 0, 100)) {
            const key = `cycleCountLevel${level}` as keyof BatteryDetectionSettings;
            this.updateSettings({ [key]: value });
        }
    }

    public setCellVoltage(value: number): void {
        if (this.validateNumericValue(value, 500, 5000)) {
            this.updateSettings({ cellVoltage: value });
        }
    }

    public setUnbalanceVoltage(value: number): void {
        if (this.validateNumericValue(value, 100, 2000)) {
            this.updateSettings({ unbalanceVoltage: value });
        }
    }

    public setCycleCountLimit(value: number): void {
        if (this.validateNumericValue(value, 100, 2000)) {
            this.updateSettings({ cycleCountLimit: value });
        }
    }

    public setTemperatureLimit(value: number): void {
        if (this.validateNumericValue(value, 0, 100)) {
            this.updateSettings({ temperatureLimit: value });
        }
    }

    public setIsolateCycle(value: number): void {
        if (this.validateNumericValue(value, 100, 2000)) {
            this.updateSettings({ isolateCycle: value });
        }
    }

    public setIsolateHealth(value: number): void {
        if (this.validateNumericValue(value, 0, 100)) {
            this.updateSettings({ isolateHealth: value });
        }
    }

    public setIsolateYearTime(value: number): void {
        if (this.validateNumericValue(value, 1, 10)) {
            this.updateSettings({ isolateYearTime: value });
        }
    }

    /**
     * 列表管理方法
     */
    public addLocation(location: string): void {
        if (location.trim() && !this.settings.locationList.includes(location.trim())) {
            const newList = [...this.settings.locationList, location.trim()];
            this.updateSettings({ locationList: newList });
        }
    }

    public removeLocation(location: string): void {
        const newList = this.settings.locationList.filter(item => item !== location);
        this.updateSettings({ locationList: newList });
    }

    public addBrokenReason(reason: string): void {
        if (reason.trim() && !this.settings.brokenReason.includes(reason.trim())) {
            const newList = [...this.settings.brokenReason, reason.trim()];
            this.updateSettings({ brokenReason: newList });
        }
    }

    public removeBrokenReason(reason: string): void {
        const newList = this.settings.brokenReason.filter(item => item !== reason);
        this.updateSettings({ brokenReason: newList });
    }

    /**
     * 數值驗證
     */
    private validateNumericValue(value: number, min: number, max: number): boolean {
        return !isNaN(value) && value >= min && value <= max;
    }

    /**
     * 添加設定變更監聽器
     */
    public addListener(listener: (settings: BatteryDetectionSettings) => void): void {
        this.listeners.push(listener);
    }

    /**
     * 移除設定變更監聽器
     */
    public removeListener(listener: (settings: BatteryDetectionSettings) => void): void {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 通知所有監聽器
     */
    private notifyListeners(): void {
        this.listeners.forEach(listener => {
            try {
                listener(this.getSettings());
            } catch (error) {
                console.error('設定變更監聽器執行失敗:', error);
            }
        });
    }

    /**
     * 匯出設定為 JSON
     */
    public exportSettings(): string {
        return JSON.stringify(this.settings, null, 2);
    }

    /**
     * 從 JSON 匯入設定
     */
    public importSettings(jsonString: string): boolean {
        try {
            const importedSettings = JSON.parse(jsonString);
            // 驗證匯入的設定格式
            if (this.validateImportedSettings(importedSettings)) {
                this.settings = { ...DEFAULT_SETTINGS, ...importedSettings };
                this.saveSettings();
                return true;
            }
        } catch (error) {
            console.error('匯入設定失敗:', error);
        }
        return false;
    }

    /**
     * 驗證匯入的設定
     */
    private validateImportedSettings(settings: any): boolean {
        // 基本類型檢查
        return typeof settings === 'object' && settings !== null;
    }
}
