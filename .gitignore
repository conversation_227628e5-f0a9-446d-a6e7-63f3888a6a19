# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

dist
dist-ssr
node_modules
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Generated by Tau<PERSON>
# will have schema files for capabilities auto-completion
gen

# Generated by Cargo
# will have compiled files and executables
target
memory-bank/
src/main/assets/backup/
yarn.lock
simulation_test.md
src/main/core/scripts/validators/README.md
src/main/core/scripts/settings/README.md
src/main/core/scripts/settings/UsageExample.ts
src/main/core/scripts/validators/BatteryValidator.test.ts
src/main/core/scripts/validators/BatteryValidatorExample.ts
src/main/core/scripts/validators/DiagnosticsIntegrationExample.ts
