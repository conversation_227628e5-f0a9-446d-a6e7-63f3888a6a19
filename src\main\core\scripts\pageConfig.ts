// 定義全域變數類型
interface PageConfigItem {
  scriptPath: string;
  title: string;
}

interface PageConfig {
  [key: string]: PageConfigItem;
}

// 確保 window 有 pageConfig 屬性
if (!(window as any).pageConfig) {
  (window as any).pageConfig = {
    'view_battery': {
      scriptPath: '/src/main/core/scripts/pages/BatteryView.ts',
      title: 'Batteries'
    },
    'view_batteryLog': {
      scriptPath: '/src/main/core/scripts/pages/BatteryLogView.ts',
      title: 'Batteries Log'
    },
    'view_chargerDetails': {
      scriptPath: '/src/main/core/scripts/pages/ChargerDetailsView.ts',
      title: 'Charger Details'
    },
    'view_settings': {
      scriptPath: '/src/main/core/scripts/pages/SettingsView.ts',
      title: 'Settings'
    },
    'view_diagnostics': {
      scriptPath: '/src/main/core/scripts/pages/DiagnosticsView.ts',
      title: 'Diagnostics'
    },
    
    // 其他頁面...
  };
}