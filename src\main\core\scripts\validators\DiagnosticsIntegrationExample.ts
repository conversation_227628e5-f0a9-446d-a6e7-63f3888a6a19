import { 
    BatteryValidator, 
    checkBatteryDamage, 
    setBatteryDamageStatus,
    printBatteryDamageReport 
} from './index';
import { BatteryData, batteryDataHelper } from '../interfaces/batteryData';

/**
 * DiagnosticsView 整合範例
 * 展示如何在 DiagnosticsView.ts 中使用共用的電池驗證器
 */

/**
 * 替換 DiagnosticsView.ts 中的電池損壞相關函數
 * 這些函數可以直接替換原有的實現
 */

/**
 * 獲取特定電池的損壞狀態（替換原有的 getBatteryDamage 函數）
 * @param batteryId 電池 ID
 * @returns 是否損壞
 */
export function getBatteryDamage(batteryId: string): boolean {
    return checkBatteryDamage(batteryId);
}

/**
 * 設定特定電池的損壞狀態（替換原有的 setBatteryDamage 函數）
 * @param batteryId 電池 ID
 * @param isDamaged 是否損壞
 */
export function setBatteryDamage(batteryId: string, isDamaged: boolean): void {
    setBatteryDamageStatus(batteryId, isDamaged);
}

/**
 * 獲取電池損壞狀態資訊（替換原有的 getBatteryDamageInfo 函數）
 * @returns 電池損壞狀態資訊物件
 */
export function getBatteryDamageInfo(): { [batteryId: string]: boolean } {
    return BatteryValidator.getBatteryDamageInfo();
}

/**
 * 儲存電池損壞狀態資訊（替換原有的 saveBatteryDamageInfo 函數）
 * @param damageInfo 電池損壞狀態資訊
 */
export function saveBatteryDamageInfo(damageInfo: { [batteryId: string]: boolean }): void {
    BatteryValidator.saveBatteryDamageInfo(damageInfo);
}

/**
 * 進階功能：驗證電池序列號並檢查損壞狀態
 * @param batteryData 電池數據
 * @returns 綜合驗證結果
 */
export function validateBatteryWithDamageCheck(batteryData: BatteryData): {
    serialValid: boolean;
    batteryType: string;
    isDamaged: boolean;
    validationMessage: string;
    damageMessage: string;
} {
    const serialValid = BatteryValidator.isSerialNumberValid(batteryData.sn);
    const batteryType = batteryDataHelper.getBatteryTypeText(batteryData.sn);
    const isDamaged = BatteryValidator.isAppearanceBroken(batteryData.id.toString());

    let validationMessage = "";
    if (!serialValid) {
        validationMessage = "Invalid serial number format or excluded date";
    } else {
        validationMessage = `Valid ${batteryType} battery`;
    }
    
    const damageMessage = isDamaged ? "Battery is marked as damaged" : "Battery appears healthy";
    
    return {
        serialValid,
        batteryType,
        isDamaged,
        validationMessage,
        damageMessage
    };
}

/**
 * 為 DiagnosticsView 創建電池卡片時的驗證輔助函數
 * @param batteryData 電池數據
 * @returns 電池狀態資訊
 */
export function getBatteryStatusForCard(batteryData: BatteryData): {
    isValidSerial: boolean;
    batteryType: string;
    isDamaged: boolean;
    statusClass: string;
    statusText: string;
} {
    const validation = validateBatteryWithDamageCheck(batteryData);
    
    let statusClass = "";
    let statusText = "";
    
    if (!validation.serialValid) {
        statusClass = "invalid-serial";
        statusText = "Invalid Serial";
    } else if (validation.isDamaged) {
        statusClass = "damaged-battery";
        statusText = "Damaged";
    } else {
        statusClass = "healthy-battery";
        statusText = "Healthy";
    }
    
    return {
        isValidSerial: validation.serialValid,
        batteryType: validation.batteryType,
        isDamaged: validation.isDamaged,
        statusClass,
        statusText
    };
}

/**
 * 生成診斷頁面的電池狀態報告
 * @param batteryDataList 電池數據列表
 */
export function generateDiagnosticsReport(batteryDataList: BatteryData[]): void {
    console.log("=== Diagnostics Battery Report ===");
    
    // 序列號驗證報告
    const validBatteries = batteryDataList.filter(battery => 
        BatteryValidator.isSerialNumberValid(battery.sn)
    );
    
    console.log(`Total Batteries: ${batteryDataList.length}`);
    console.log(`Valid Serial Numbers: ${validBatteries.length}`);
    console.log(`Invalid Serial Numbers: ${batteryDataList.length - validBatteries.length}`);
    
    // 損壞狀態報告
    printBatteryDamageReport(batteryDataList);
    
    // 詳細狀態
    console.log("\nDetailed Battery Status:");
    batteryDataList.forEach(battery => {
        const status = getBatteryStatusForCard(battery);
        console.log(`Battery ${battery.id} (${battery.sn}): ${status.batteryType} - ${status.statusText}`);
    });
}

/**
 * 切換電池損壞狀態的輔助函數（用於 UI 切換按鈕）
 * @param batteryId 電池 ID
 * @returns 新的損壞狀態
 */
export function toggleBatteryDamageStatus(batteryId: string): boolean {
    const currentStatus = checkBatteryDamage(batteryId);
    const newStatus = !currentStatus;
    setBatteryDamageStatus(batteryId, newStatus);
    return newStatus;
}

/**
 * 檢查是否有任何電池損壞
 * @returns 是否有損壞的電池
 */
export function hasAnyDamagedBatteries(): boolean {
    const damagedIds = BatteryValidator.getDamagedBatteryIds();
    return damagedIds.length > 0;
}

/**
 * 獲取損壞電池的數量
 * @returns 損壞電池數量
 */
export function getDamagedBatteryCount(): number {
    return BatteryValidator.getDamagedBatteryIds().length;
}

/**
 * 使用範例：如何在 DiagnosticsView.ts 中整合
 * 
 * 1. 導入共用驗證器：
 *    import { getBatteryDamage, setBatteryDamage, validateBatteryWithDamageCheck } from '../validators/DiagnosticsIntegrationExample';
 * 
 * 2. 替換現有函數：
 *    - 將原有的 getBatteryDamage 函數替換為導入的版本
 *    - 將原有的 setBatteryDamage 函數替換為導入的版本
 *    - 移除原有的 getBatteryDamageInfo 和 saveBatteryDamageInfo 函數
 * 
 * 3. 在創建電池卡片時使用新的驗證：
 *    const batteryStatus = getBatteryStatusForCard(batteryData);
 *    // 使用 batteryStatus 來設定卡片的樣式和狀態
 * 
 * 4. 在需要時生成報告：
 *    generateDiagnosticsReport(currentBatteryData);
 */
