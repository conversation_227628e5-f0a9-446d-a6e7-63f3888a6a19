{"name": "package", "private": true, "version": "0.25.318", "type": "module", "scripts": {"dev": "vite --config src/vite.config.ts", "build": "tsc && vite build --config src/vite.config.ts", "preview": "vite preview --config src/vite.config.ts", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-http": "^2.4.4", "@tauri-apps/plugin-opener": "^2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@tauri-apps/cli": "^2", "autoprefixer": "^10.4.21", "postcss": "^8.4.31", "tailwindcss": "^3.4.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}