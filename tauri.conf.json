{"$schema": "https://schema.tauri.app/config/2", "productName": "UPowerApplication", "version": "0.25.318", "identifier": "com.onyx.app", "build": {"devUrl": "http://localhost:1420", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "frontendDist": "dist"}, "app": {"withGlobalTauri": true, "windows": [{"url": "src/main/res/layout/activity_main.html", "title": "UPowerApplication", "width": 1100, "height": 768, "minWidth": 800, "minHeight": 600}], "security": {"csp": null, "assetProtocol": {"enable": true, "scope": {"requireLiteralLeadingDot": false, "allow": ["**/*"]}}}}, "bundle": {"active": true, "targets": "all", "icon": ["src/main/assets/32x32.png", "src/main/assets/128x128.png", "src/main/assets/<EMAIL>", "src/main/assets/icon.icns", "src/main/assets/icon.ico"], "resources": ["src/main/assets/UPower-SDK.dll", "src/main/assets/128x128.png", "src/main/assets/*", "src/main/res/**/*", "src/main/core/scripts/*", "src/main/core/scripts/**/*"]}}