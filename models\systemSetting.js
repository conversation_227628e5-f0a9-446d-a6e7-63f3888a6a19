'use strict';

module.exports = (sequelize, DataTypes) => {
  const systemSetting = sequelize.define('systemSetting', {
    key: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'string'
    }
  }, {
    tableName: 'system_settings'
  });
  
  return systemSetting;
};